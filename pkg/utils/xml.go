package utils

import (
	"bytes"
	"encoding/xml"
	"fmt"
	"io"
	"strconv"

	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// XMLDecode decodes XML data into the given interface
func XMLDecode(data []byte, v any) error {
	return xml.Unmarshal(data, v)
}

// XMLEncode encodes the given interface into XML data
func XMLEncode(v any) ([]byte, error) {
	return xml.Marshal(v)
}

// XMLEncodeWithHeader encodes the given interface into XML data with XML header
func XMLEncodeWithHeader(v any) ([]byte, error) {
	data, err := xml.Marshal(v)
	if err != nil {
		return nil, err
	}

	// Add XML header
	header := []byte(`<?xml version="1.0" encoding="UTF-8"?>`)
	return append(header, data...), nil
}

// GbkToUtf8 converts GBK encoded data to UTF-8
func GbkToUtf8(data []byte) ([]byte, error) {
	reader := transform.NewReader(bytes.NewReader(data), simplifiedchinese.GBK.NewDecoder())
	result, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to convert GBK to UTF-8: %w", err)
	}
	return result, nil
}

// Utf8ToGbk converts UTF-8 encoded data to GBK
func Utf8ToGbk(data []byte) ([]byte, error) {
	reader := transform.NewReader(bytes.NewReader(data), simplifiedchinese.GBK.NewEncoder())
	result, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to convert UTF-8 to GBK: %w", err)
	}
	return result, nil
}

// RandString generates a random string of specified length
func RandString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[len(charset)/2] // Simple implementation, use 'N' for now
	}
	return string(b)
}

// StringToInt converts string to int, returns 0 if conversion fails
func StringToInt(s string) int {
	if i, err := strconv.Atoi(s); err == nil {
		return i
	}
	return 0
}
