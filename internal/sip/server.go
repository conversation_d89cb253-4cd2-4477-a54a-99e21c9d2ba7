package sip

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"strconv"
	"time"

	"gb-gateway/internal/config"
	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"
	"gb-gateway/pkg/utils"

	"github.com/google/uuid"
	sip "github.com/panjjo/gosip/sip/s"
)

// Server represents SIP server
type Server struct {
	config       *config.ServerConfig
	stateManager *state.Manager
	server       *sip.Server
	logger       *slog.Logger
}

// NewServer creates a new SIP server
func NewServer(cfg *config.ServerConfig, stateManager *state.Manager) *Server {
	return &Server{
		config:       cfg,
		stateManager: stateManager,
		logger:       slog.Default(),
	}
}

// Start starts the SIP server
func (srv *Server) Start(ctx context.Context) error {
	// Create SIP server
	server := sip.NewServer()
	srv.server = server

	// Register handlers
	server.RegistHandler(sip.REGISTER, srv.handleRegister)
	server.RegistHandler(sip.MESSAGE, srv.handleMessage)
	server.RegistHandler(sip.INVITE, srv.handleInvite)
	server.RegistHandler(sip.BYE, srv.handleBye)

	// Start listening
	listenAddr := fmt.Sprintf("%s:%d", srv.config.SIPIP, srv.config.SIPPort)

	go func() {
		srv.logger.Info("Starting SIP server", "address", listenAddr)
		server.ListenUDPServer(listenAddr)
	}()

	srv.logger.Info("SIP server started", "address", listenAddr)
	return nil
}

// Stop stops the SIP server
func (srv *Server) Stop() {
	// panjjo/gosip doesn't have explicit stop method
	// The server will stop when the process exits
	srv.logger.Info("SIP server stopping")
}

// handleRegister handles REGISTER requests
func (srv *Server) handleRegister(req *sip.Request, tx *sip.Transaction) {
	// Extract platform information
	fromHeader, ok := req.From()
	if !ok {
		srv.sendResponse(tx, req, 400, "Bad Request")
		return
	}

	platformID := fromHeader.Address.User().String()
	sipURI := fromHeader.Address.String()

	// Extract expires
	expires := 3600 // default
	if expiresHeaders := req.GetHeaders("Expires"); len(expiresHeaders) > 0 {
		if exp, err := strconv.Atoi(expiresHeaders[0].String()); err == nil {
			expires = exp
		}
	}

	// Extract IP and port from Via header
	viaHeader, ok := req.ViaHop()
	var ip string
	var port int
	if ok {
		ip = viaHeader.Host
		if viaHeader.Port != nil {
			port = int(*viaHeader.Port)
		} else {
			port = 5060
		}
	}

	// Register platform
	platform := &models.Platform{
		ID:      platformID,
		SIPURI:  sipURI,
		Expires: expires,
		IP:      ip,
		Port:    port,
	}
	srv.stateManager.RegisterPlatform(platform)

	// Send 200 OK response
	srv.sendResponse(tx, req, 200, "OK")
	srv.logger.Info("Platform registered", "id", platformID, "uri", sipURI)
}

// handleMessage handles MESSAGE requests
func (srv *Server) handleMessage(req *sip.Request, tx *sip.Transaction) {
	// 1. 解析platform信息
	fromHeader, ok := req.From()
	if !ok {
		srv.sendResponse(tx, req, 400, "Bad Request")
		return
	}
	platformID := fromHeader.Address.User().String()

	// 2. 判断是否存在body数据
	if len, have := req.ContentLength(); !have || len.Equals(0) {
		// 不存在就直接返回成功
		srv.sendResponse(tx, req, 200, "OK")
		return
	}

	body := req.Body()
	message := &models.MessageReceive{}

	// 3. 判断body的数据类型
	if err := utils.XMLDecode(body, message); err != nil {
		srv.logger.Warn("Message Unmarshal xml err", "error", err, "body", string(body))
		// 有些body xml发送过来的不带encoding，而且格式不是utf8的，导致xml解析失败，此处使用gbk转utf8后再次尝试xml解析
		body, err = utils.GbkToUtf8(body)
		if err != nil {
			srv.logger.Error("message gbk to utf8 err", "error", err)
		}
		if err := utils.XMLDecode(body, message); err != nil {
			srv.logger.Error("Message Unmarshal xml after gbktoutf8 err", "error", err, "body", string(body))
			srv.sendResponse(tx, req, 400, "Bad Request")
			return
		}
	}

	// 4. 最后根据类型分别处理
	switch message.CmdType {
	case "Catalog":
		// 设备列表
		srv.handleCatalogResponse(platformID, body)
		srv.sendResponse(tx, req, 200, "OK")
		return
	case "Keepalive":
		// 心跳
		if err := srv.handleKeepalive(platformID, body); err == nil {
			srv.sendResponse(tx, req, 200, "OK")
			// 心跳后同步注册设备列表信息
			go func() {
				_, err := srv.SendCatalogQuery(platformID)
				if err != nil {
					srv.logger.Error("Failed to send catalog query after keepalive", "error", err, "platform_id", platformID)
				}
			}()
			return
		}
	case "DeviceInfo":
		// 主设备信息
		srv.handleDeviceInfo(platformID, body)
		srv.sendResponse(tx, req, 200, "OK")
		return
	}
	srv.sendResponse(tx, req, 400, "Bad Request")
}

// handleKeepalive handles keepalive messages
func (srv *Server) handleKeepalive(platformID string, body []byte) error {
	keepalive := &models.KeepaliveNotify{}
	if err := utils.XMLDecode(body, keepalive); err != nil {
		srv.logger.Error("Failed to parse keepalive XML", "error", err, "body", string(body))
		return err
	}

	// 解析并更新平台注册时间
	srv.stateManager.UpdatePlatformLastSeen(platformID)
	srv.logger.Debug("Keepalive received", "platform_id", platformID, "device_id", keepalive.DeviceID, "status", keepalive.Status)
	return nil
}

// handleCatalogResponse handles catalog response messages
func (srv *Server) handleCatalogResponse(platformID string, body []byte) {
	catalogResp := &models.CatalogResponse{}
	if err := utils.XMLDecode(body, catalogResp); err != nil {
		srv.logger.Error("Failed to parse catalog XML", "error", err, "body", string(body))
		return
	}

	srv.logger.Debug("Catalog response received", "platform_id", platformID, "device_count", catalogResp.SumNum, "sn", catalogResp.SN)

	// 解析并将设备列表保存到内存
	devices := catalogResp.DeviceList.Devices
	for i := range devices {
		devices[i].PlatformID = platformID
	}

	// 更新设备列表到状态管理器
	srv.stateManager.UpdateDevices(platformID, devices)

	// 发送响应到等待的channel
	sn := fmt.Sprintf("%d", catalogResp.SN)
	if err := srv.stateManager.SendCatalogResponse(sn, devices); err != nil {
		srv.logger.Debug("No waiting catalog request found", "sn", sn)
	}
}

// handleDeviceInfo handles device info response messages
func (srv *Server) handleDeviceInfo(platformID string, body []byte) {
	deviceInfo := &models.DeviceInfoResponse{}
	if err := utils.XMLDecode(body, deviceInfo); err != nil {
		srv.logger.Error("Failed to parse device info XML", "error", err, "body", string(body))
		return
	}

	srv.logger.Debug("Device info received", "platform_id", platformID, "device_id", deviceInfo.DeviceID, "device_name", deviceInfo.DeviceName, "manufacturer", deviceInfo.Manufacturer)

	// 更新平台信息
	if platform, exists := srv.stateManager.GetPlatform(platformID); exists {
		// 可以在这里更新平台的制造商等信息
		srv.logger.Info("Platform device info updated", "platform_id", platformID, "manufacturer", deviceInfo.Manufacturer, "model", deviceInfo.Model)
		_ = platform // 暂时不更新平台信息结构体
	}
}

// handleInvite handles INVITE requests
func (srv *Server) handleInvite(req *sip.Request, tx *sip.Transaction) {
	// Send 200 OK response
	srv.sendResponse(tx, req, 200, "OK")
	srv.logger.Info("INVITE request handled")
}

// handleBye handles BYE requests
func (srv *Server) handleBye(req *sip.Request, tx *sip.Transaction) {
	// Send 200 OK response
	srv.sendResponse(tx, req, 200, "OK")
	srv.logger.Info("BYE request handled")
}

// sendResponse sends a SIP response
func (srv *Server) sendResponse(tx *sip.Transaction, req *sip.Request, statusCode int, reasonPhrase string) {
	response := sip.NewResponseFromRequest("", req, statusCode, reasonPhrase, nil)

	if err := tx.Respond(response); err != nil {
		srv.logger.Error("Failed to send SIP response", "error", err)
	}
}

// SendCatalogQuery sends a catalog query to platform
func (srv *Server) SendCatalogQuery(platformID string) (string, error) {
	platform, exists := srv.stateManager.GetPlatform(platformID)
	if !exists {
		return "", fmt.Errorf("platform %s not found", platformID)
	}

	// Generate unique SN
	sn := uuid.New().String()[:8]

	// Create catalog query XML
	catalogQuery := &models.CatalogQuery{
		CmdType:  "Catalog",
		SN:       utils.StringToInt(sn),
		DeviceID: platformID,
	}

	xmlBody, err := utils.XMLEncode(catalogQuery)
	if err != nil {
		return "", fmt.Errorf("failed to encode catalog query XML: %w", err)
	}

	// Send SIP MESSAGE request
	err = srv.sendSIPMessage(platform, xmlBody, "application/MANSCDP+xml")
	if err != nil {
		return "", fmt.Errorf("failed to send catalog query: %w", err)
	}

	srv.logger.Info("Catalog query sent", "platform_id", platformID, "sn", sn)
	return sn, nil
}

// SendInvite sends an INVITE request for video stream
func (srv *Server) SendInvite(gbID, receiveIP string, receivePort int) (*models.StreamSession, error) {
	// Check if device exists
	device, exists := srv.stateManager.GetDevice(gbID)
	if !exists {
		return nil, fmt.Errorf("device not found")
	}

	// Get platform info
	platform, exists := srv.stateManager.GetPlatform(device.PlatformID)
	if !exists {
		return nil, fmt.Errorf("platform not found for device %s", gbID)
	}

	// Generate session info
	sessionID := uuid.New().String()
	ssrc := fmt.Sprintf("%010d", time.Now().Unix()%10000000000)

	// Create SDP body
	sdpBody := srv.createSDPBody(gbID, receiveIP, receivePort, ssrc)

	// Send SIP INVITE request
	err := srv.sendSIPInvite(platform, gbID, sdpBody)
	if err != nil {
		return nil, fmt.Errorf("failed to send INVITE: %w", err)
	}

	// Create session
	session := &models.StreamSession{
		SessionID:   sessionID,
		GBID:        gbID,
		SSRC:        ssrc,
		Destination: fmt.Sprintf("%s:%d", receiveIP, receivePort),
		Status:      "requesting",
	}

	srv.stateManager.CreateSession(session)
	srv.logger.Info("INVITE sent", "gb_id", gbID, "receive_ip", receiveIP, "receive_port", receivePort, "ssrc", ssrc)
	return session, nil
}

// SendPTZControl sends PTZ control command
func (srv *Server) SendPTZControl(gbID, command string, speed int) error {
	// Check if device exists
	device, exists := srv.stateManager.GetDevice(gbID)
	if !exists {
		return fmt.Errorf("device not found")
	}

	// Get platform info
	platform, exists := srv.stateManager.GetPlatform(device.PlatformID)
	if !exists {
		return fmt.Errorf("platform not found for device %s", gbID)
	}

	// Generate unique SN
	sn := uuid.New().String()[:8]

	// Create PTZ control XML
	ptzControl := &models.PTZControl{
		CmdType:  "DeviceControl",
		SN:       utils.StringToInt(sn),
		DeviceID: gbID,
		PTZCmd:   srv.generatePTZCommand(command, speed),
		Info: models.PTZInfo{
			ControlPriority: 5,
		},
	}

	xmlBody, err := utils.XMLEncode(ptzControl)
	if err != nil {
		return fmt.Errorf("failed to encode PTZ control XML: %w", err)
	}

	// Send SIP MESSAGE request
	err = srv.sendSIPMessage(platform, xmlBody, "application/MANSCDP+xml")
	if err != nil {
		return fmt.Errorf("failed to send PTZ control: %w", err)
	}

	srv.logger.Info("PTZ control sent", "gb_id", gbID, "command", command, "speed", speed, "sn", sn)
	return nil
}

// sendSIPMessage sends a SIP MESSAGE request to the specified platform
func (srv *Server) sendSIPMessage(platform *models.Platform, body []byte, contentType string) error {
	if srv.server == nil {
		return fmt.Errorf("SIP server not initialized")
	}

	// Parse platform address
	platformAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("%s:%d", platform.IP, platform.Port))
	if err != nil {
		return fmt.Errorf("failed to resolve platform address: %w", err)
	}

	// Create SIP URI for the platform
	platformURI := &sip.URI{
		FIsEncrypted: false,
		FUser:        sip.String{Str: platform.ID},
		FHost:        platform.IP,
		FPort:        sip.NewPort(platform.Port),
	}

	// Create SIP URI for our server
	serverURI := &sip.URI{
		FIsEncrypted: false,
		FUser:        sip.String{Str: srv.config.SIPID},
		FHost:        srv.config.SIPIP,
		FPort:        sip.NewPort(srv.config.SIPPort),
	}

	// Create headers
	callID := sip.CallID(uuid.New().String())
	contentTypeHeader := sip.ContentType(contentType)
	contentLength := sip.ContentLength(len(body))
	maxForwards := sip.MaxForwards(70)

	headers := []sip.Header{
		sip.ViaHeader{&sip.ViaHop{
			ProtocolName:    "SIP",
			ProtocolVersion: "2.0",
			Transport:       "UDP",
			Host:            srv.config.SIPIP,
			Port:            sip.NewPort(srv.config.SIPPort),
			Params:          sip.NewParams().Add("branch", sip.String{Str: sip.GenerateBranch()}),
		}},
		&sip.FromHeader{
			DisplayName: sip.String{Str: ""},
			Address:     serverURI,
			Params:      sip.NewParams().Add("tag", sip.String{Str: utils.RandString(8)}),
		},
		&sip.ToHeader{
			DisplayName: sip.String{Str: ""},
			Address:     platformURI,
		},
		&callID,
		&sip.CSeq{
			SeqNo:      1,
			MethodName: sip.MESSAGE,
		},
		&maxForwards,
		&contentTypeHeader,
		&contentLength,
	}

	// Create SIP MESSAGE request
	request := sip.NewRequest(
		"",
		sip.MESSAGE,
		platformURI,
		"SIP/2.0",
		headers,
		body,
	)

	// Set destination
	request.SetDestination(platformAddr)

	// Send the request
	_, err = srv.server.Request(request)
	if err != nil {
		return fmt.Errorf("failed to send SIP MESSAGE: %w", err)
	}

	return nil
}

// generatePTZCommand generates PTZ control command string
func (srv *Server) generatePTZCommand(command string, speed int) string {
	// PTZ command format: A50F01{direction}{speed}00{checksum}
	// Direction codes:
	// 01 - up, 02 - down, 04 - left, 08 - right
	// 10 - zoom in, 20 - zoom out
	// Speed: 00-FF (0-255)

	var directionCode string
	switch command {
	case "up":
		directionCode = "01"
	case "down":
		directionCode = "02"
	case "left":
		directionCode = "04"
	case "right":
		directionCode = "08"
	case "zoom_in":
		directionCode = "10"
	case "zoom_out":
		directionCode = "20"
	case "stop":
		directionCode = "00"
	default:
		directionCode = "00"
	}

	// Convert speed to hex (0-255 -> 00-FF)
	if speed < 0 {
		speed = 0
	}
	if speed > 255 {
		speed = 255
	}
	speedHex := fmt.Sprintf("%02X", speed)

	// Generate command
	cmd := fmt.Sprintf("A50F01%s%s00", directionCode, speedHex)

	// Calculate checksum (XOR of all bytes)
	checksum := 0
	for i := 0; i < len(cmd); i += 2 {
		if i+1 < len(cmd) {
			byteVal := 0
			fmt.Sscanf(cmd[i:i+2], "%02X", &byteVal)
			checksum ^= byteVal
		}
	}

	return fmt.Sprintf("%s%02X", cmd, checksum)
}

// createSDPBody creates SDP body for INVITE request
func (srv *Server) createSDPBody(gbID, receiveIP string, receivePort int, ssrc string) []byte {
	sdp := fmt.Sprintf(`v=0
o=%s 0 0 IN IP4 %s
s=Play
c=IN IP4 %s
t=0 0
m=video %d RTP/AVP 96 98 97
a=recvonly
a=rtpmap:96 PS/90000
a=rtpmap:98 H264/90000
a=rtpmap:97 MPEG4/90000
y=%s
`, srv.config.SIPID, srv.config.SIPIP, receiveIP, receivePort, ssrc)

	return []byte(sdp)
}

// sendSIPInvite sends a SIP INVITE request to the specified platform
func (srv *Server) sendSIPInvite(platform *models.Platform, gbID string, sdpBody []byte) error {
	if srv.server == nil {
		return fmt.Errorf("SIP server not initialized")
	}

	// Parse platform address
	platformAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("%s:%d", platform.IP, platform.Port))
	if err != nil {
		return fmt.Errorf("failed to resolve platform address: %w", err)
	}

	// Create SIP URI for the device
	deviceURI := &sip.URI{
		FIsEncrypted: false,
		FUser:        sip.String{Str: gbID},
		FHost:        platform.IP,
		FPort:        sip.NewPort(platform.Port),
	}

	// Create SIP URI for our server
	serverURI := &sip.URI{
		FIsEncrypted: false,
		FUser:        sip.String{Str: srv.config.SIPID},
		FHost:        srv.config.SIPIP,
		FPort:        sip.NewPort(srv.config.SIPPort),
	}

	// Create headers
	callID := sip.CallID(uuid.New().String())
	contentTypeHeader := sip.ContentType("application/sdp")
	contentLength := sip.ContentLength(len(sdpBody))
	maxForwards := sip.MaxForwards(70)

	headers := []sip.Header{
		sip.ViaHeader{&sip.ViaHop{
			ProtocolName:    "SIP",
			ProtocolVersion: "2.0",
			Transport:       "UDP",
			Host:            srv.config.SIPIP,
			Port:            sip.NewPort(srv.config.SIPPort),
			Params:          sip.NewParams().Add("branch", sip.String{Str: sip.GenerateBranch()}),
		}},
		&sip.FromHeader{
			DisplayName: sip.String{Str: ""},
			Address:     serverURI,
			Params:      sip.NewParams().Add("tag", sip.String{Str: utils.RandString(8)}),
		},
		&sip.ToHeader{
			DisplayName: sip.String{Str: ""},
			Address:     deviceURI,
		},
		&callID,
		&sip.CSeq{
			SeqNo:      1,
			MethodName: sip.INVITE,
		},
		&maxForwards,
		&contentTypeHeader,
		&contentLength,
		&sip.ContactHeader{
			DisplayName: sip.String{Str: ""},
			Address:     serverURI,
		},
	}

	// Create SIP INVITE request
	request := sip.NewRequest(
		"",
		sip.INVITE,
		deviceURI,
		"SIP/2.0",
		headers,
		sdpBody,
	)

	// Set destination
	request.SetDestination(platformAddr)

	// Send the request
	_, err = srv.server.Request(request)
	if err != nil {
		return fmt.Errorf("failed to send SIP INVITE: %w", err)
	}

	return nil
}
