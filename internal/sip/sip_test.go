package sip

import (
	"context"
	"testing"
	"time"

	"gb-gateway/internal/config"
	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"
)

func TestSIPMessageSending(t *testing.T) {
	// Create test configuration
	cfg := &config.ServerConfig{
		SIPIP:     "127.0.0.1",
		SIPPort:   5060,
		SIPID:     "34020000002000000001",
		SIPDomain: "3402000000",
	}

	// Create state manager
	stateManager := state.NewManager()

	// Create SIP server
	server := NewServer(cfg, stateManager)

	// Start server in background
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		err := server.Start(ctx)
		if err != nil {
			t.Errorf("Failed to start SIP server: %v", err)
		}
	}()

	// Wait for server to start
	time.Sleep(100 * time.Millisecond)

	// Register a test platform
	platform := &models.Platform{
		ID:       "34020000002000000002",
		SIPURI:   "sip:34020000002000000002@*************:5060",
		Expires:  3600,
		IP:       "*************",
		Port:     5060,
		LastSeen: time.Now(),
	}
	stateManager.RegisterPlatform(platform)

	// Register a test device
	device := &models.Device{
		GBID:       "34020000001320000001",
		Name:       "Test Camera",
		Status:     "ON",
		PlatformID: platform.ID,
	}
	stateManager.UpdateDevices(platform.ID, []models.Device{*device})

	t.Run("TestSendCatalogQuery", func(t *testing.T) {
		// Note: This will fail with network error in test environment, but we can verify the logic
		sn, err := server.SendCatalogQuery(platform.ID)
		if err != nil {
			// Expected to fail due to network, but check if it's the right kind of error
			if !contains(err.Error(), "can't assign requested address") && !contains(err.Error(), "failed to send SIP MESSAGE") {
				t.Errorf("Unexpected error type: %v", err)
			}
			t.Logf("Expected network error: %v", err)
		} else {
			if sn == "" {
				t.Error("Expected non-empty SN")
			}
			t.Logf("Catalog query sent with SN: %s", sn)
		}
	})

	t.Run("TestSendPTZControl", func(t *testing.T) {
		// Note: This will fail with network error in test environment, but we can verify the logic
		err := server.SendPTZControl(device.GBID, "up", 128)
		if err != nil {
			// Expected to fail due to network, but check if it's the right kind of error
			if !contains(err.Error(), "can't assign requested address") && !contains(err.Error(), "failed to send PTZ control") {
				t.Errorf("Unexpected error type: %v", err)
			}
			t.Logf("Expected network error: %v", err)
		} else {
			t.Log("PTZ control command sent successfully")
		}
	})

	t.Run("TestSendInvite", func(t *testing.T) {
		// Note: This will fail with network error in test environment, but we can verify the logic
		session, err := server.SendInvite(device.GBID, "*************", 8000)
		if err != nil {
			// Expected to fail due to network, but check if it's the right kind of error
			if !contains(err.Error(), "can't assign requested address") && !contains(err.Error(), "failed to send INVITE") {
				t.Errorf("Unexpected error type: %v", err)
			}
			t.Logf("Expected network error: %v", err)
		} else {
			if session == nil {
				t.Error("Expected non-nil session")
			} else {
				if session.GBID != device.GBID {
					t.Errorf("Expected GBID %s, got %s", device.GBID, session.GBID)
				}
				t.Logf("INVITE sent successfully, session ID: %s, SSRC: %s", session.SessionID, session.SSRC)
			}
		}
	})

	t.Run("TestGeneratePTZCommand", func(t *testing.T) {
		testCases := []struct {
			command        string
			speed          int
			expectedPrefix string
		}{
			{"up", 128, "A50F010180"},
			{"down", 64, "A50F010240"},
			{"left", 255, "A50F0104FF"},
			{"right", 0, "A50F010800"},
			{"zoom_in", 100, "A50F011064"},
			{"zoom_out", 200, "A50F0120C8"},
			{"stop", 0, "A50F010000"},
		}

		for _, tc := range testCases {
			result := server.generatePTZCommand(tc.command, tc.speed)
			// We can't predict the exact checksum, but we can verify the format
			if len(result) != 14 { // A50F01 + direction + speed + 00 + checksum = 14 chars
				t.Errorf("Expected PTZ command length 14, got %d for command %s", len(result), tc.command)
			}
			if result[:6] != "A50F01" {
				t.Errorf("Expected PTZ command to start with A50F01, got %s for command %s", result[:6], tc.command)
			}
			if !contains(result, tc.expectedPrefix) {
				t.Errorf("Expected PTZ command to contain %s, got %s for command %s", tc.expectedPrefix, result, tc.command)
			}
			t.Logf("PTZ command for %s (speed %d): %s", tc.command, tc.speed, result)
		}
	})

	t.Run("TestCreateSDPBody", func(t *testing.T) {
		sdpBody := server.createSDPBody("34020000001320000001", "*************", 8000, "1234567890")
		if len(sdpBody) == 0 {
			t.Error("Expected non-empty SDP body")
		}
		sdpStr := string(sdpBody)
		if !contains(sdpStr, "v=0") {
			t.Error("SDP body should contain version line")
		}
		if !contains(sdpStr, "*************") {
			t.Error("SDP body should contain receive IP")
		}
		if !contains(sdpStr, "8000") {
			t.Error("SDP body should contain receive port")
		}
		if !contains(sdpStr, "1234567890") {
			t.Error("SDP body should contain SSRC")
		}
		t.Logf("SDP body created:\n%s", sdpStr)
	})
}

func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func TestSIPServerLifecycle(t *testing.T) {
	cfg := &config.ServerConfig{
		SIPIP:     "127.0.0.1",
		SIPPort:   5061, // Use different port to avoid conflicts
		SIPID:     "34020000002000000001",
		SIPDomain: "3402000000",
	}

	stateManager := state.NewManager()
	server := NewServer(cfg, stateManager)

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	// Test server start
	err := server.Start(ctx)
	if err != nil {
		t.Errorf("Failed to start SIP server: %v", err)
	}

	// Test server stop
	server.Stop()
	t.Log("SIP server lifecycle test completed")
}
