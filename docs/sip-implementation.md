# SIP 消息发送功能实现文档

## 概述

本文档描述了使用 panjjo/gosip 库实现的 SIP 消息发送功能，包括：

1. **SIP MESSAGE 请求** - 用于目录查询和 PTZ 控制
2. **SIP INVITE 请求** - 用于推流到指定位置

## 实现的功能

### 1. SIP MESSAGE 请求发送

#### 目录查询 (Catalog Query)
- **方法**: `SendCatalogQuery(platformID string) (string, error)`
- **功能**: 向指定平台发送设备目录查询请求
- **XML 格式**: 
  ```xml
  <?xml version="1.0" encoding="UTF-8"?>
  <Query>
    <CmdType>Catalog</CmdType>
    <SN>12345678</SN>
    <DeviceID>34020000002000000002</DeviceID>
  </Query>
  ```

#### PTZ 控制 (PTZ Control)
- **方法**: `SendPTZControl(gbID, command string, speed int) error`
- **功能**: 向指定设备发送云台控制命令
- **支持的命令**:
  - `up` - 向上
  - `down` - 向下
  - `left` - 向左
  - `right` - 向右
  - `zoom_in` - 放大
  - `zoom_out` - 缩小
  - `stop` - 停止
- **XML 格式**:
  ```xml
  <?xml version="1.0" encoding="UTF-8"?>
  <Control>
    <CmdType>DeviceControl</CmdType>
    <SN>12345678</SN>
    <DeviceID>34020000001320000001</DeviceID>
    <PTZCmd>A50F010180002A</PTZCmd>
    <Info>
      <ControlPriority>5</ControlPriority>
    </Info>
  </Control>
  ```

### 2. SIP INVITE 请求发送

#### 视频流请求
- **方法**: `SendInvite(gbID, receiveIP string, receivePort int) (*models.StreamSession, error)`
- **功能**: 向指定设备发送视频流邀请请求
- **SDP 内容**:
  ```
  v=0
  o=34020000002000000001 0 0 IN IP4 127.0.0.1
  s=Play
  c=IN IP4 *************
  t=0 0
  m=video 8000 RTP/AVP 96 98 97
  a=recvonly
  a=rtpmap:96 PS/90000
  a=rtpmap:98 H264/90000
  a=rtpmap:97 MPEG4/90000
  y=1234567890
  ```

## 核心实现

### 1. SIP 消息发送核心方法

```go
func (srv *Server) sendSIPMessage(platform *models.Platform, body []byte, contentType string) error
```

该方法负责构造和发送 SIP MESSAGE 请求，包括：
- 创建正确的 SIP URI
- 构造必要的 SIP 头部
- 发送消息到目标平台

### 2. SIP INVITE 发送核心方法

```go
func (srv *Server) sendSIPInvite(platform *models.Platform, gbID string, sdpBody []byte) error
```

该方法负责构造和发送 SIP INVITE 请求，包括：
- 创建 SDP 会话描述
- 构造 INVITE 请求头部
- 发送邀请到目标设备

### 3. PTZ 命令生成

```go
func (srv *Server) generatePTZCommand(command string, speed int) string
```

生成符合 GB28181 标准的 PTZ 控制命令：
- 格式: `A50F01{direction}{speed}00{checksum}`
- 方向码: 01(上), 02(下), 04(左), 08(右), 10(放大), 20(缩小)
- 速度: 00-FF (0-255)
- 校验和: 所有字节的异或值

### 4. SDP 内容生成

```go
func (srv *Server) createSDPBody(gbID, receiveIP string, receivePort int, ssrc string) []byte
```

生成符合标准的 SDP 会话描述，包括：
- 媒体类型和端口
- 编解码器信息 (PS/H264/MPEG4)
- SSRC 标识

## 测试验证

### 单元测试
- **文件**: `internal/sip/sip_test.go`
- **覆盖范围**:
  - SIP 消息发送逻辑
  - PTZ 命令生成
  - SDP 内容生成
  - 服务器生命周期

### 演示程序
- **文件**: `examples/sip_demo.go`
- **功能**: 展示所有 SIP 消息发送功能的使用方法

## 使用示例

### 发送目录查询
```go
sn, err := server.SendCatalogQuery("34020000002000000002")
if err != nil {
    log.Printf("Failed to send catalog query: %v", err)
} else {
    log.Printf("Catalog query sent with SN: %s", sn)
}
```

### 发送 PTZ 控制
```go
err := server.SendPTZControl("34020000001320000001", "up", 128)
if err != nil {
    log.Printf("Failed to send PTZ control: %v", err)
} else {
    log.Println("PTZ control sent successfully")
}
```

### 发送 INVITE 请求
```go
session, err := server.SendInvite("34020000001320000001", "*************", 8000)
if err != nil {
    log.Printf("Failed to send INVITE: %v", err)
} else {
    log.Printf("INVITE sent, session: %s, SSRC: %s", session.SessionID, session.SSRC)
}
```

## 技术细节

### 依赖库
- **panjjo/gosip**: SIP 协议栈实现
- **github.com/google/uuid**: UUID 生成
- **golang.org/x/text**: 字符编码转换

### SIP 头部构造
所有 SIP 消息都包含标准头部：
- Via: 路由信息
- From: 发送方信息
- To: 接收方信息
- Call-ID: 会话标识
- CSeq: 序列号和方法
- Max-Forwards: 最大转发次数
- Content-Type: 内容类型
- Content-Length: 内容长度

### 错误处理
- 网络连接错误
- 平台/设备不存在错误
- XML 编码错误
- SIP 消息构造错误

## 注意事项

1. **网络环境**: 在测试环境中，由于没有真实的 GB28181 设备，会出现网络连接错误，这是正常现象
2. **消息格式**: 所有 XML 消息都使用 UTF-8 编码
3. **SSRC 生成**: 使用时间戳生成唯一的 SSRC 标识
4. **校验和计算**: PTZ 命令使用 XOR 校验和确保数据完整性

## 扩展功能

未来可以扩展的功能：
- 支持更多 PTZ 控制命令
- 实现 SIP BYE 请求用于结束会话
- 添加 SIP 认证支持
- 支持 TCP 传输协议
- 实现录像回放控制
