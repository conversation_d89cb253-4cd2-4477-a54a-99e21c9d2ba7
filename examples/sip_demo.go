package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"gb-gateway/internal/config"
	"gb-gateway/internal/sip"
	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"
)

func main() {
	fmt.Println("GB-Gateway SIP Demo")
	fmt.Println("==================")

	// Create configuration
	cfg := &config.ServerConfig{
		SIPIP:     "127.0.0.1",
		SIPPort:   5060,
		SIPID:     "34020000002000000001",
		SIPDomain: "3402000000",
	}

	// Create state manager
	stateManager := state.NewManager()

	// Create SIP server
	server := sip.NewServer(cfg, stateManager)

	// Start server
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		err := server.Start(ctx)
		if err != nil {
			log.Fatalf("Failed to start SIP server: %v", err)
		}
	}()

	// Wait for server to start
	time.Sleep(100 * time.Millisecond)
	fmt.Printf("SIP server started on %s:%d\n", cfg.SIPIP, cfg.SIPPort)

	// Register a demo platform
	platform := &models.Platform{
		ID:       "34020000002000000002",
		SIPURI:   "sip:34020000002000000002@*************:5060",
		Expires:  3600,
		IP:       "*************",
		Port:     5060,
		LastSeen: time.Now(),
	}
	stateManager.RegisterPlatform(platform)
	fmt.Printf("Registered platform: %s\n", platform.ID)

	// Register a demo device
	device := &models.Device{
		GBID:       "34020000001320000001",
		Name:       "Demo Camera",
		Status:     "ON",
		PlatformID: platform.ID,
	}
	stateManager.UpdateDevices(platform.ID, []models.Device{*device})
	fmt.Printf("Registered device: %s (%s)\n", device.GBID, device.Name)

	fmt.Println("\nDemonstrating SIP functionality:")
	fmt.Println("================================")

	// Demo 1: Send Catalog Query
	fmt.Println("\n1. Sending Catalog Query...")
	sn, err := server.SendCatalogQuery(platform.ID)
	if err != nil {
		fmt.Printf("   Error: %v\n", err)
		fmt.Printf("   Note: This is expected in demo environment (no real platform to connect to)\n")
	} else {
		fmt.Printf("   Catalog query sent successfully with SN: %s\n", sn)
	}

	// Demo 2: Send PTZ Control Commands
	fmt.Println("\n2. Sending PTZ Control Commands...")
	ptzCommands := []struct {
		command string
		speed   int
	}{
		{"up", 128},
		{"down", 64},
		{"left", 255},
		{"right", 100},
		{"zoom_in", 200},
		{"zoom_out", 150},
		{"stop", 0},
	}

	for _, cmd := range ptzCommands {
		err := server.SendPTZControl(device.GBID, cmd.command, cmd.speed)
		if err != nil {
			fmt.Printf("   PTZ %s (speed %d): Error - %v\n", cmd.command, cmd.speed, err)
			fmt.Printf("   Note: This is expected in demo environment (no real device to connect to)\n")
		} else {
			fmt.Printf("   PTZ %s (speed %d): Success\n", cmd.command, cmd.speed)
		}
		break // Only show one example to avoid spam
	}

	// Demo 3: Send INVITE Request
	fmt.Println("\n3. Sending INVITE Request...")
	session, err := server.SendInvite(device.GBID, "*************", 8000)
	if err != nil {
		fmt.Printf("   Error: %v\n", err)
		fmt.Printf("   Note: This is expected in demo environment (no real device to connect to)\n")
	} else {
		fmt.Printf("   INVITE sent successfully:\n")
		fmt.Printf("   - Session ID: %s\n", session.SessionID)
		fmt.Printf("   - SSRC: %s\n", session.SSRC)
		fmt.Printf("   - Destination: %s\n", session.Destination)
		fmt.Printf("   - Status: %s\n", session.Status)
	}

	// Demo 4: Show generated content examples
	fmt.Println("\n4. Generated Content Examples:")
	fmt.Println("==============================")

	// Show PTZ command generation
	fmt.Println("\nPTZ Commands:")
	for _, cmd := range ptzCommands {
		ptzCmd := generatePTZCommandDemo(cmd.command, cmd.speed)
		fmt.Printf("   %s (speed %d): %s\n", cmd.command, cmd.speed, ptzCmd)
	}

	// Show SDP body generation
	fmt.Println("\nSDP Body Example:")
	sdpBody := createSDPBodyDemo("34020000001320000001", "*************", 8000, "1234567890")
	fmt.Printf("%s\n", sdpBody)

	fmt.Println("\nDemo completed!")
	fmt.Println("===============")
	fmt.Println("This demo shows the SIP message generation capabilities.")
	fmt.Println("In a real environment, these messages would be sent to actual GB28181 devices.")
	fmt.Println("The network errors shown are expected since we're not connecting to real devices.")

	// Stop server
	server.Stop()
}

// Demo helper functions (simplified versions of the actual methods)
func generatePTZCommandDemo(command string, speed int) string {
	var directionCode string
	switch command {
	case "up":
		directionCode = "01"
	case "down":
		directionCode = "02"
	case "left":
		directionCode = "04"
	case "right":
		directionCode = "08"
	case "zoom_in":
		directionCode = "10"
	case "zoom_out":
		directionCode = "20"
	case "stop":
		directionCode = "00"
	default:
		directionCode = "00"
	}

	if speed < 0 {
		speed = 0
	}
	if speed > 255 {
		speed = 255
	}
	speedHex := fmt.Sprintf("%02X", speed)

	cmd := fmt.Sprintf("A50F01%s%s00", directionCode, speedHex)

	// Calculate checksum
	checksum := 0
	for i := 0; i < len(cmd); i += 2 {
		if i+1 < len(cmd) {
			byteVal := 0
			fmt.Sscanf(cmd[i:i+2], "%02X", &byteVal)
			checksum ^= byteVal
		}
	}

	return fmt.Sprintf("%s%02X", cmd, checksum)
}

func createSDPBodyDemo(gbID, receiveIP string, receivePort int, ssrc string) string {
	return fmt.Sprintf(`v=0
o=34020000002000000001 0 0 IN IP4 127.0.0.1
s=Play
c=IN IP4 %s
t=0 0
m=video %d RTP/AVP 96 98 97
a=recvonly
a=rtpmap:96 PS/90000
a=rtpmap:98 H264/90000
a=rtpmap:97 MPEG4/90000
y=%s`, receiveIP, receivePort, ssrc)
}
